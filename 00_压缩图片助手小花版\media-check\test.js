/**
 * 简单的测试脚本
 * 用于测试微信内容安全检测API
 */
const axios = require('axios');
const config = require('./config');

// 测试配置
const testConfig = {
  apiUrl: 'http://localhost:8848/api/security-check',
  mediaUrl: 'https://example.com/test-image.jpg', // 替换为实际的媒体URL
  mediaType: 2, // 2表示图片
  appId: config.wechat.apps[0].appId // 使用第一个配置的小程序
};

/**
 * 测试内容安全检测API
 */
async function testSecurityCheck() {
  try {
    console.log('开始测试内容安全检测API...');

    const response = await axios.post(testConfig.apiUrl, {
      mediaUrl: testConfig.mediaUrl,
      mediaType: testConfig.mediaType,
      appId: testConfig.appId
    });

    console.log('API响应:', JSON.stringify(response.data, null, 2));

    if (response.data.code === 0) {
      console.log('测试成功!');
    } else {
      console.error('测试失败:', response.data.msg);
    }
  } catch (error) {
    console.error('测试出错:', error.message);
    if (error.response) {
      console.error('错误响应:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 执行测试
testSecurityCheck();
