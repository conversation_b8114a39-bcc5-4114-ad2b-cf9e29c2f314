<template>
  <button class="share-btn" open-type="share">
    <image class="share-icon" src="/static/share.svg" mode="aspectFit"></image>
    <text class="share-text">分享</text>
  </button>
</template>

<script>
export default {
  name: 'Share<PERSON>utton',
  props: {
    title: {
      type: String,
      default: '图片压缩'
    },
    path: {
      type: String,
      default: '/pages/index/index'
    },
    imageUrl: {
      type: String,
      default: ''
    }
  }
}
</script>

<style>
.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  height: 32px;
  border-radius: 16px;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: none;
}

.share-icon {
  width: 16px;
  height: 16px;
}

.share-text {
  font-size: 14px;
  color: #333333;
  margin-left: 4px;
  line-height: 1;
}
</style> 